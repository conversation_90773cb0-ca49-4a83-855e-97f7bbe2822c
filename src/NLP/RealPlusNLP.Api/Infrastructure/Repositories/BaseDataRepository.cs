using System.Data;
using Dapper;
using Microsoft.Data.SqlClient;
using RealPlusNLP.Api.Abstractions.Repositories;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Infrastructure.Repositories;

public abstract class BaseDataRepository
    : IDataRepository
{
    protected readonly string _connectionString;

    public BaseDataRepository(IConfiguration configuration)
    {
        _connectionString = GetConnectionString(configuration);
    }

    public async Task<IReadOnlyCollection<AddressModel>> GetAddressesAsync(
        int BuildingNumberStart,
        int BuildingNumberEnd,
        string BuildingNumberFull,
        string FullAddress,
        string StreetName,
        CancellationToken cancellationToken = default)
    {
        using var connection = new SqlConnection(_connectionString);

        var parameters = new DynamicParameters();
        parameters.Add("@BuildingName", null, DbType.String);
        parameters.Add("@HouseNumber1", BuildingNumberStart, DbType.Int32);
        parameters.Add("@HouseNumber2", BuildingNumberEnd, DbType.Int32);
        parameters.Add("@HouseNumberFull", BuildingNumberFull, DbType.String);
        parameters.Add("@SearchLocation", FullAddress, DbType.String);
        parameters.Add("@StreetName", StreetName, DbType.String);
        parameters.Add("@Omni", FullAddress, DbType.String);
        parameters.Add("@CouldBeAStreetFlag", 0, DbType.Int32);
        parameters.Add("@ListingCategoryId", 8, DbType.Int32);

        var results = await connection.QueryAsync<AddressResultRecord>(
            "upGetOmniSearchResult",
            parameters,
            commandType: CommandType.StoredProcedure);

        var addressModels = results
            .Select(r => new AddressModel(
                r.ListText,
                ParseCommaSeparatedInts(r.ListName)))
            .ToList();

        return addressModels.AsReadOnly();
    }

    protected abstract string GetConnectionString(IConfiguration configuration);

    private static int[] ParseCommaSeparatedInts(string commaSeparatedValues)
    {
        if (string.IsNullOrWhiteSpace(commaSeparatedValues))
            return [];

        return [.. commaSeparatedValues
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(s => s.Trim())
            .Where(s => int.TryParse(s, out _))
            .Select(int.Parse)];
    }
}

internal record AddressResultRecord(string ListName, string ListText);
