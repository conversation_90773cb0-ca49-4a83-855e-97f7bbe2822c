using RealPlusNLP.Api.Abstractions.Repositories;
using RealPlusNLP.Api.Common.Enums;

namespace RealPlusNLP.Api.Infrastructure.Repositories;

public class RepositoryFactory(
    ReSourceRepository resourceRepository,
    AllAccessNYCRepository allAccessNYCRepository)
    : IRepositoryFactory
{
    public IDataRepository Get(DeploymentType deploymentType)
    {
        return deploymentType switch
        {
            DeploymentType.Resource => resourceRepository,
            DeploymentType.AllAccessNYC => allAccessNYCRepository,
            _ => throw new ArgumentOutOfRangeException(nameof(deploymentType))
        };
    }
}
