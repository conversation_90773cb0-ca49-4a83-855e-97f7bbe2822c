﻿using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Enums;
using RealPlusNLP.Api.Common.Models;
using System.ComponentModel;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class NeighborhoodPlugin(INeighborhoodService neighborhoodService)
{
    static readonly HashSet<string> exclusionWords = new(StringComparer.OrdinalIgnoreCase)
    {
        "coop", "coops", "condo", "condos", "condop", "condops", "townhouse", "townhouses", "rental", "rentals", "sales", "sale"
    };

    [Description("""
        Returns the NYC neighborhood ids and names for given city area names and types.
        If you see something like:
        - Downtown
        - Downtown in Manhattan
        - Downtown in New York (NYC)
        then you MUST use CityAreaType.Section for city area type.
        For Downtown Brooklyn use CityAreaType.Neighborhood.
    """)]
    public NeighborhoodModel[] GetNeighborhoods(
        [Description("A list of city area names and their corresponding types")]
        List<CityArea> cityAreas)
    {
        var filteredCityAreas = cityAreas
            .Where(city => !string.IsNullOrWhiteSpace(city.Name) &&
                !exclusionWords.Contains(city.Name.Trim()))
            .ToList();
        if (filteredCityAreas.Count == 0)
            return [];

        return [.. filteredCityAreas
            .SelectMany(d => neighborhoodService.Search(d.Name, d.Type))
            .Distinct()];
    }
}

[Description("NYC city areas and their types")]
public record CityArea(
    [property: Description("City area name")] string Name,
    [property: Description("City area type")] CityAreaType Type);
