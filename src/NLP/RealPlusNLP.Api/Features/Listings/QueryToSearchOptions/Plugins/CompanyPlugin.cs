using System.ComponentModel;
using RealPlusNLP.Api.Abstractions.Services;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class CompanyPlugin(IElasticsearchService elasticsearchService)
{
    static readonly HashSet<string> exclusionWords = new(StringComparer.OrdinalIgnoreCase)
    {
        "coop", "coops", "condo", "condos", "condop", "condops", "townhouse", "townhouses", "rental", "rentals", "sales", "sale"
    };

    [Description("Finds the NYC real estate exclusive companies (brokerages) by partial names match and returns a fully qualified company names. Call this tool ONLY for actual brokerage company names like '<PERSON><PERSON>oran', 'Douglas Elliman', 'Compass'. Do NOT call for ownership types (coop, condo, rental) or property categories.")]
    public async Task<string[]> GetExclusiveCompanies(
        [Description(@"Partial or full names of NYC real estate exclusive companies (brokerages, e.g., '<PERSON>rcoran', 'Elliman').")]
        List<string> companyNames,
        CancellationToken cancellationToken = default)
    {
        var filteredCompanyNames = companyNames
            .Where(name => !string.IsNullOrWhiteSpace(name) && !exclusionWords.Contains(name.Trim()))
            .ToList();
        if (filteredCompanyNames.Count == 0)
            return [];

        var searchTasks = filteredCompanyNames
            .Select(companyName => elasticsearchService.SearchCompaniesAsync(companyName, cancellationToken));

        var searchResults = await Task.WhenAll(searchTasks);

        var companies = searchResults
            .SelectMany(result => result)
            .Select(company => company.CompanyName)
            .ToArray();

        return companies;
    }
}
