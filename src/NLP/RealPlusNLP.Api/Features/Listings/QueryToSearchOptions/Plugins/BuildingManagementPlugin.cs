using System.ComponentModel;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class BuildingManagementPlugin(IElasticsearchService elasticsearchService)
{
    static readonly HashSet<string> exclusionWords = new(StringComparer.OrdinalIgnoreCase)
    {
        "coop", "coops", "condo", "condos", "condop", "condops", "townhouse", "townhouses", "rental", "rentals", "sales", "sale"
    };

    [Description("Finds the building management companies/owners/landlords in the New York City for given names. You MUST ALWAYS ignore next separate single words that are not part of the name of the building management company/owner/landlord: 'coop', 'rental'.")]
    public async Task<BuildingManagementModel?> GetBuildingManagement(
        [Description("A list of the building management company/owner/landlord names in the New York City.")]
        List<string> buildingManagementNames,
        CancellationToken cancellationToken = default)
    {
        var filteredBuildingManagementNames = buildingManagementNames
            .Where(name => !string.IsNullOrWhiteSpace(name) && !exclusionWords.Contains(name.Trim()))
            .ToList();
        if (filteredBuildingManagementNames.Count == 0)
            return null;

        var searchTasks = filteredBuildingManagementNames
            .Select(management => elasticsearchService.SearchBuildingManagementAsync(management, cancellationToken));

        var searchResults = await Task.WhenAll(searchTasks);

        var (resultCompanies, resultOwners, resultLandlords) = searchResults
            .Aggregate(
                (Companies: new HashSet<string>(), Owners: new HashSet<string>(), Landlords: new HashSet<string>()),
                (acc, result) => (
                    acc.Companies.Union(result.Companies).ToHashSet(),
                    acc.Owners.Union(result.Owners).ToHashSet(),
                    acc.Landlords.Union(result.Landlords).ToHashSet()
                ),
                acc => (acc.Companies.ToArray(), acc.Owners.ToArray(), acc.Landlords.ToArray()));

        return new BuildingManagementModel(resultCompanies, resultOwners, resultLandlords);
    }
}
