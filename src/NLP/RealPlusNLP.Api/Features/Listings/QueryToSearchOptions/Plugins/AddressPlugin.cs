using System.ComponentModel;
using RealPlusNLP.Api.Abstractions.Repositories;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

public class AddressPlugin(
    IRepositoryFactory repositoryFactory,
    IUserContextService userContextService)
{
    [Description("Returns the list of found addresses in the New York City")]
    public async Task<AddressModel[]> GetAddresses(
        [Description("A list of addresses in the New York City")]
        List<AddressSearchModel> addresses,
        CancellationToken cancellationToken = default)
    {
        var repository = repositoryFactory.Get(userContextService.GetDeploymentType());

        var searchTasks = addresses.Select(address => repository.GetAddressesAsync(
            address.BuildingNumberStart,
            address.BuildingNumberEnd,
            address.BuildingNumberFull,
            address.FullAddress,
            address.StreetName,
            cancellationToken));

        var searchResults = await Task.WhenAll(searchTasks);

        var result = searchResults
            .SelectMany(result => result)
            .ToArray();

        return result;
    }
}

[Description("The New York City address")]
public sealed record AddressSearchModel(
    [property: Description("If building number is a range, then this is the start of the range. If it is not a range, then this is the building number. Sample #1: 215 WEST 90TH STREET, then BuildingNumberStart is 215. Sample #2: 215-219 WEST 90TH STREET, then BuildingNumberStart is 215.")]
    int BuildingNumberStart,
    [property: Description("If building number is a range, then this is the end of the range. If it is not a range, then set 0 to BuildingNumberEnd. Sample #1: 215 WEST 90TH STREET, then BuildingNumberEnd is 0. Sample #2: 215-219 WEST 90TH STREET, then BuildingNumberEnd is 219.")]
    int BuildingNumberEnd,
    [property: Description("Full building number as it is in the address. Set to null if building number is not presented in the address. Sample #1: 215 WEST 90TH STREET, then BuildingNumberFull is 215. Sample #2: 215-219 WEST 90TH STREET, then BuildingNumberFull is 215-219. Sample #3: WEST 90TH STREET, then BuildingNumberFull is null.")]
    string BuildingNumberFull,
    [property: Description("Full address as it is in the query.")]
    string FullAddress,
    [property: Description("Street name as it is in the query. Sample #1: 215 WEST 90TH STREET, then StreetName is WEST 90TH STREET.")]
    string StreetName);
