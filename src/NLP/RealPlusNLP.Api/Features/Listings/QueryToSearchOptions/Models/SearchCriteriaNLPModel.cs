using System.ComponentModel;
using System.Text.Json.Serialization;
using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Models;

public sealed record SearchCriteriaNLPModel(
    [property: Description("""
    **YOU MUST FOLLOW NEXT RULES for ListingCategory:**
    - **If OwnershipType contains '25', ListingCategory MUST be set to Rentals.**
    - **If you see 'rental ownership' or 'ownership rental' set to Rentals.**
    - **You ALWAYS MUST set to Sales (8) when neither 'rentals' nor 'sales' is mentioned.**
    """)]
    ListingCategoryType ListingCategory,
    string? BuildingPeriods,
    string? OwnershipType,
    string? Amenities,
    string? AttendedLobby,
    [property: JsonPropertyName("bedrooms_min")] string? BedroomsMin,
    [property: JsonPropertyName("bedrooms_max")] string? BedroomsMax,
    [property: Description("""
    Set to false if, and ONLY if:
    - 'exact' or 'only' word is used before number of bedrooms or 'studio'.
    - no range, such as 'no more than', 'no less than', 'more than', 'less than', 'min', 'max', '1-2', etc, is specified.
    Sample to set BedroomsMore to false: I am looking for coop listings with 2 bedrooms

    Otherwise set to true.
    """)]
    bool BedroomsMore,
    [property: JsonPropertyName("bathrooms_min")] string? BathroomsMin,
    [property: JsonPropertyName("bathrooms_max")] string? BathroomsMax,
    [property: Description("""
    Set to false if, and ONLY if:
    - 'exact' or 'only' word is used before number of bathrooms.
    - no range, such as 'no more than', 'no less than', 'more than', 'less than', 'min', 'max', '1-2', etc, is specified.
    Sample to set BathroomsMore to false: I am looking for coop listings with 2 bathrooms

    Otherwise set to true.
    """)]
    bool BathroomsMore,
    [property: JsonPropertyName("rooms_min")] double? RoomsMin,
    [property: JsonPropertyName("rooms_max")] double? RoomsMax,
    [property: Description("""
    Set to false if, and ONLY if:
    - 'exact', 'exactly', 'only' or similar (synonym) word is used before number of rooms.
    - no range, such as 'no more than', 'no less than', 'more than', 'less than', 'min', 'max', '1-2', etc, is specified.
    Sample to set RoomsMore to false: I am looking for coop listings with 2 rooms

    Otherwise set to true.
    """)]
    bool RoomsMore,
    [property: Description("""
    Set the amount to both min and max price fields if, and ONLY if:
    - 'exact', 'exactly', 'only' or similar (synonym) word is used before the price.
    - no range, such as 'no more than', 'no less than', 'more than', 'less than', 'min', 'max', '100-200', etc, is specified.
    Sample to set the price to both min and max fields: 'I am looking for a condo with $1,000,000.'
    """)]
    [property: JsonPropertyName("price_min")] string? PriceMin,
    [property: JsonPropertyName("price_max")] string? PriceMax,
    [property: JsonPropertyName("sqft_min")]
    [property: Description("""
    Min apartment square feet (size). Should be less than or equal to sqft_max
    Set the size to both min and max sqft fields if, and ONLY if:
    - 'exact', 'exactly', 'only' or similar (synonym) word is used before the size.
    - no range, such as 'no more than', 'no less than', 'more than', 'less than', 'min', 'max', '100-200', etc, is specified.
    Sample to set the price to both min and max fields: 'I am looking for a condo with 1000 sqft.'
    """)]
    double? SqFtMin,
    [property: JsonPropertyName("sqft_max")]
    [property: Description("Max apartment square feet (size). Should be more than or equal to sqft_min")]
    double? SqFtMax,
    [property: Description("a list of neighborhoods (id and name)")]
    NeighborhoodModel[]? Neighborhoods,
    [property: Description("User can use short names for statuses. e.g. CS for ContractSigned, LS for LeaseSigned, etc.")]
    ListingStatus[]? ListingStatus,
    [property: Description("List of the New York City building names and their related RPBin numbers")]
    BuildingModel[]? Buildings,
    [property: Description("Open house date range. Can be shorten to OH. Always should be more than or equal to today's date.")]
    DateRange? OpenHouseDateRange,
    [property: Description("Always should be less than or equal to today's date.")]
    ListedUpdatedDateRange? ListedOrUpdatedDateRange,
    [property: Description("Applicable for both Contract Signed and Lease Signed. Applicable for both Sales and Rentals. Always should be less than or equal to today's date. When a value is set in this field, you MUST add ContractSigned for Sales or LeaseSigned for Rentals to the ListingStatus if not specified yet.")]
    DateRange? ContractLeaseSignedDateRange,
    [property: Description("Applicable for both Sold and Rented. Applicable for both Sales and Rentals. Always should be less than or equal to today's date. When a value is set in this field, you MUST add Sold for Sales or Rented for Rentals to the ListingStatus if not specified yet.")]
    DateRange? SoldRentedDateRange,
    [property: Description("A list of school names in the New York City and their related school ids")]
    SchoolModel[]? Schools,
    [property: Description("A list of the NYC Real Estate company names (brokerages). Call a proper tool to get the full company names.")]
    string[]? ExclusiveCompanies,
    [property: Description("Building management companies/owners/landlords in the New York City. Applicable only to ListingCategory = 'Rentals'. You MUST to ignore this field for ListingCategory = 'Sales' and set it to null.")]
    BuildingManagementModel? BuildingManagement,
    [property: Description("Listing types")]
    ListingType[]? ListingTypes,
    [property: Description("The New York City address")]
    AddressModel[]?Addresses,
    [property: Description("The number of the unit in the building. Can be written as 'apartment' or 'apt'. Usually follows after the building address. Can be separated from the address by comma or space. Can have 'Unit' or '#' if front of the unit number. Can be a number, a letter, or a combination of both. Sample #1: 215 WEST 90TH STREET UNIT 10A, then UnitNo is 10A. Sample #2: 215 WEST 90TH STREET #10A, then UnitNo is 10A.")]
    string? UnitNo,
    [property: JsonPropertyName("unprocessed_criteria")] string? UnprocessedCriteria);

[Description("Date range. Start date MUST be always less than or equal to the end date. Always ignore time part of the dates, keep only date part, set time part to 00:00:00. Weekend means Saturday and Sunday.")]
public record DateRange(DateTime StartDate, DateTime EndDate);

public sealed record ListedUpdatedDateRange(
    DateTime StartDate, DateTime EndDate, ListedUpdatedActivityType ListedUpdatedActivity)
    : DateRange(StartDate, EndDate);

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("'Listed or updated', or 'listed', or 'updated' activity types, different for Sales and Rentals.")]
public enum ListedUpdatedActivityType
{
    [Description("Apply ONLY, and **ONLY**, when BOTH words 'updated' and 'listed' are mentioned together. Can be written as 'listed/updated' or 'listed or updated'. Applicable ONLY to ListingCategory = 'Sales'.")]
    ListedUpdatedForSalesOnly = 1552,
    [Description("Apply ONLY when SINGLE word 'listed' is mentioned. Applicable ONLY to ListingCategory = 'Sales'.")]
    ListedForSalesOnly = 1553,
    [Description("Apply ONLY when SINGLE word 'updated' is mentioned. Applicable ONLY to ListingCategory = 'Sales'.")]
    UpdatedForSalesOnly = 1554,
    [Description("Apply ONLY, and **ONLY**, when BOTH words 'updated' and 'listed' are mentioned together. Can be written as 'listed/updated' or 'listed or updated'. Applicable ONLY to ListingCategory = 'Rentals'.")]
    ListedUpdatedForRentalsOnly = 3701,
    [Description("Apply ONLY when SINGLE word 'listed' is mentioned. Applicable ONLY to ListingCategory = 'Rentals'.")]
    ListedForRentalsOnly = 3702,
    [Description("Apply ONLY when SINGLE word 'updated' is mentioned. Applicable ONLY to ListingCategory = 'Rentals'.")]
    UpdatedForRentalsOnly = 3703
}

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("Listing category types: Sales or Rentals.")]
public enum ListingCategoryType
{
    [Description("Can be written as 'to sale', or 'for sale', or 'to buy', or 'for buy'.")]
    Sales = 8,
    [Description("Can be written as 'to rent', or 'for rent(al)'.")]
    Rentals = 7
}

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("Status of the listing")]
public enum ListingStatus
{
    [Description("Status of the sale and rental 'active' listings")]
    Active,
    [Description("Status of the sale 'contract signed' listings. Can be written as 'contracted'. Apply this status when ContractLeaseSignedDateRange for Sales is specified.")]
    ContractSigned,
    [Description("Status of the rental 'lease signed' listings. Can be written as 'leased'. Apply this status when ContractLeaseSignedDateRange for Rentals is specified.")]
    LeaseSigned,
    [Description("Status of the sale 'sold' listings. Apply this status when SoldRentedDateRange for Sales is specified.")]
    Sold,
    [Description("Status of the rental 'rented' listings. Apply this status when SoldRentedDateRange for Rentals is specified.")]
    Rented,
    [Description("Status of the sale and rental 'closed' listings")]
    Closed,
    [Description("Status of the sale and rental 'off market' listings")]
    OffMarket
}

[JsonConverter(typeof(JsonStringEnumConverter))]
[Description("Listing type")]
public enum ListingType
{
    [Description("Applicable ONLY to ListingCategory = 'Sales'.")]
    ExclusiveForSaleOnly = 14,
    [Description("co-exclusive. Applicable ONLY to ListingCategory = 'Sales'.")]
    CoExclusiveForSaleOnly = 13,
    [Description("Applicable ONLY to ListingCategory = 'Sales'.")]
    OpenForSaleOnly = 17,
    [Description("Applicable ONLY to ListingCategory = 'Sales'.")]
    ForSaleByOwner = 15,
    [Description("Applicable ONLY to ListingCategory = 'Rentals'.")]
    ExclusiveForRentalOnly = 3523,
    [Description("co-exclusive. Applicable ONLY to ListingCategory = 'Rentals'.")]
    CoExclusiveForRentalOnly = 3524,
    [Description("Applicable ONLY to ListingCategory = 'Rentals'.")]
    OpenForRentalOnly = 3527,
    [Description("Reserve/Opt-Out. Applicable to both ListingCategory = 'Sales' and to ListingCategory = 'Rentals'.")]
    ReserveOptOut = 3876,
    [Description("RLS: Participant Only.Applicable to both ListingCategory = 'Sales' and to ListingCategory = 'Rentals'.")]
    RlsParticipantOnly = 3899
}