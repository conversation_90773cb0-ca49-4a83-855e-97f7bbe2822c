﻿using RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Models;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions;

public sealed class MappingConfig : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        config.NewConfig<SearchCriteriaNLPModel, SearchCriteriaViewModel>()
            .Map(dest => dest.listingCategoryId, src => (int)src.ListingCategory)
            .Map(dest => dest.buildingPeriods, src => src.BuildingPeriods)
            .Map(dest => dest.ownershipType, src => src.OwnershipType)
            .Map(dest => dest.amenities, src => src.Amenities)
            .Map(dest => dest.attendedLobby, src => src.AttendedLobby)
            .Map(dest => dest.pricemin, src => src.PriceMin)
            .Map(dest => dest.pricemax, src => src.PriceMax)
            .Map(dest => dest.rooms, src => src.RoomsMin)
            .Map(dest => dest.maxrooms, src => src.RoomsMax)
            .Map(dest => dest.roomsPlus, src => src.RoomsMore)
            .Map(dest => dest.bathrooms, src => src.BathroomsMin)
            .Map(dest => dest.maxbathrooms, src => src.BathroomsMax)
            .Map(dest => dest.bathroomsPlus, src => src.BathroomsMore)
            .Map(dest => dest.bedrooms, src => src.BedroomsMin)
            .Map(dest => dest.maxbedrooms, src => src.BedroomsMax)
            .Map(dest => dest.bedroomsPlus, src => src.BedroomsMore)
            .Map(dest => dest.minSqft, src => src.SqFtMin)
            .Map(dest => dest.maxSqft, src => src.SqFtMax)
            .Map(dest => dest.areaOrNeighborhood,
                src => src.Neighborhoods == null || src.Neighborhoods.Length == 0
                ? null : string.Join(",", src.Neighborhoods.Select(x => x.Id)))
            .Map(dest => dest.tempAreaOrNeighborhoodIds,
                src => src.Neighborhoods == null || src.Neighborhoods.Length == 0
                ? null : string.Join("$$", src.Neighborhoods.Select(x => x.Id)))
            .Map(dest => dest.addressOrBuildingName, src => GetAddressOrBuildingNames(src))
            .Map(dest => dest.tempAddressOrBuildingIds, src => GetAddressOrBuildingIds(src))
            .Map(dest => dest.addressOrBuildingIds, src => GetAddressOrBuildingIds(src))
            .Map(dest => dest.quickSearchInfo, src => GetQuickSearchInfo(src))
            .Map(dest => dest.listingCategorystatus,
                src => src.ListingStatus == null || src.ListingStatus.Length == 0
                ? null : string.Join(",", GetListingStatuses(src.ListingStatus, src.ListingCategory)))
            .Map(dest => dest.openHouseStartDate, src => src.OpenHouseDateRange != null ? (DateTime?)src.OpenHouseDateRange.StartDate : null)
            .Map(dest => dest.openHouseEndDate, src => src.OpenHouseDateRange != null ? (DateTime?)src.OpenHouseDateRange.EndDate : null)
            .Map(dest => dest.openHouseType, src => src.OpenHouseDateRange == null ? null : "any")
            .Map(dest => dest.listUpdateStartDate, src => src.ListedOrUpdatedDateRange != null ? (DateTime?)src.ListedOrUpdatedDateRange.StartDate : null)
            .Map(dest => dest.listUpdateEndDate, src => src.ListedOrUpdatedDateRange != null ? (DateTime?)src.ListedOrUpdatedDateRange.EndDate : null)
            .Map(dest => dest.listedAndUpdatedActivity, src => src.ListedOrUpdatedDateRange == null ? 0 : src.ListedOrUpdatedDateRange.ListedUpdatedActivity)
            .Map(dest => dest.contractSignedStartDate, src => src.ContractLeaseSignedDateRange != null ? (DateTime?)src.ContractLeaseSignedDateRange.StartDate : null)
            .Map(dest => dest.contractSignedEndDate, src => src.ContractLeaseSignedDateRange != null ? (DateTime?)src.ContractLeaseSignedDateRange.EndDate : null)
            .Map(dest => dest.soldStartDate, src => src.SoldRentedDateRange != null ? (DateTime?)src.SoldRentedDateRange.StartDate : null)
            .Map(dest => dest.soldEndDate, src => src.SoldRentedDateRange != null ? (DateTime?)src.SoldRentedDateRange.EndDate : null)
            .Map(dest => dest.schoolIds, src => src.Schools == null || src.Schools.Length == 0
                ? null : src.Schools.Select(x => x.Id).ToArray())
            .Map(dest => dest.schoolIdsWithZone, src => src.Schools == null || src.Schools.Length == 0
                ? null : src.Schools.Select(x => x.Id).ToList())
            .Map(dest => dest.schoolNames, src => src.Schools == null || src.Schools.Length == 0
                ? null : src.Schools.Select(x => x.Name).ToList())
            .Map(dest => dest.searchByExclusiveFirm,
                src => src.ExclusiveCompanies == null || src.ExclusiveCompanies.Length == 0
                ? null : string.Join("$$", src.ExclusiveCompanies.Select(x => $"Company: {x}")))
            .Map(dest => dest.managementCompanies,
                src => src.BuildingManagement != null && src.BuildingManagement.Companies.Length != 0
                ? src.BuildingManagement.Companies : null)
            .Map(dest => dest.owners,
                src => src.BuildingManagement != null && src.BuildingManagement.Owners.Length != 0
                ? src.BuildingManagement.Owners : null)
            .Map(dest => dest.landlords,
                src => src.BuildingManagement != null && src.BuildingManagement.Landlords.Length != 0
                ? src.BuildingManagement.Landlords : null)
            .Map(dest => dest.listingType,
                src => src.ListingTypes == null || src.ListingTypes.Length == 0
                ? null : string.Join(",", src.ListingTypes.Select(x => (int)x)))
            .Map(dest => dest.unitNo, src => string.IsNullOrEmpty(src.UnitNo)
                ? null : $"{src.UnitNo}$$")
            // defaults
            .Map(dest => dest.listingStatus, src => string.Empty);
    }

    static string? GetAddressOrBuildingIds(SearchCriteriaNLPModel model)
    {
        var result = string.Empty;

        if (model.Buildings != null && model.Buildings.Length != 0)
        {
            result += string.Join("$$", model.Buildings.Select(x =>
                string.Join(",", x.RpBins.Select(x => x.ToString()))));
        }
        if (model.Addresses != null && model.Addresses.Length != 0)
        {
            if (!string.IsNullOrEmpty(result))
            {
                result += "$$";
            }
            result += string.Join("$$", model.Addresses.Select(x =>
                string.Join(",", x.RpBins.Select(x => x.ToString()))));
        }

        return string.IsNullOrEmpty(result) ? null : result;
    }

    static string? GetAddressOrBuildingNames(SearchCriteriaNLPModel model)
    {
        var result = string.Empty;

        if (model.Buildings != null && model.Buildings.Length != 0)
        {
            result += string.Join("$$", model.Buildings.Select(x => x.Name));
        }
        if (model.Addresses != null && model.Addresses.Length != 0)
        {
            if (!string.IsNullOrEmpty(result))
            {
                result += "$$";
            }
            result += string.Join("$$", model.Addresses.Select(x => x.Address
                .Replace("Address: ", "").Replace("Street: ", "")));
        }

        return string.IsNullOrEmpty(result) ? null : result;
    }

    static string? GetQuickSearchInfo(SearchCriteriaNLPModel model)
    {
        var result = string.Empty;

        if (model.Neighborhoods != null && model.Neighborhoods.Length != 0)
        {
            result += string.Join("$$", model.Neighborhoods.Select(x => $"Neighborhood: {x.Name}"));
        }
        if (model.Buildings != null && model.Buildings.Length != 0)
        {
            if (!string.IsNullOrEmpty(result))
            {
                result += "$$";
            }
            result += string.Join("$$", model.Buildings.Select(x => $"Building Name: {x.Name}"));
        }
        if (model.Schools != null && model.Schools.Length != 0)
        {
            if (!string.IsNullOrEmpty(result))
            {
                result += "$$";
            }
            result += string.Join("$$", model.Schools.Select(x => $"School: {x.Name}"));
        }
        if (model.Addresses != null && model.Addresses.Length != 0)
        {
            if (!string.IsNullOrEmpty(result))
            {
                result += "$$";
            }
            result += string.Join("$$", model.Addresses.Select(x => x.Address));
        }

        return string.IsNullOrEmpty(result) ? null : result;
    }

    static int[] GetListingStatuses(ListingStatus[] listingStatuses, ListingCategoryType listingCategory) =>
        listingStatuses?.SelectMany(x => (IEnumerable<int>)(x switch
        {
            ListingStatus.Active => listingCategory switch
            {
                ListingCategoryType.Sales => [163, 3593, 3594, 3688, 3595, 3687, 3596, 3689, 3848],
                ListingCategoryType.Rentals => [163, 3593, 3599, 3600, 3601, 3694, 3692, 3693],
                _ => throw new ArgumentOutOfRangeException(nameof(listingCategory))
            },
            ListingStatus.ContractSigned => [3312, 3323, 3306, 3277, 3280],
            ListingStatus.LeaseSigned => [219, 3695, 3597],
            ListingStatus.Sold => [156, 3598],
            ListingStatus.Rented => [220, 3602],
            ListingStatus.Closed => listingCategory switch
            {
                ListingCategoryType.Sales => [156, 3598],
                ListingCategoryType.Rentals => [220, 3602],
                _ => throw new ArgumentOutOfRangeException(nameof(listingCategory))
            },
            ListingStatus.OffMarket => [220, 3602],
            _ => throw new ArgumentOutOfRangeException(nameof(x))
        })).ToArray() ?? [];
}
