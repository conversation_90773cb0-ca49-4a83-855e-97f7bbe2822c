using Microsoft.Extensions.AI;
using NJsonSchema;
using RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Models;
using RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

namespace RealPlusNLP.Api.Features.Listings.QueryToSearchOptions;

public static class Handler
{
    public sealed record Request(string SearchText) : IRequest<Response>;

    public sealed record Response(
        SearchCriteriaViewModel SearchOptions,
        string? UnprocessedCriteria);

    public sealed class RequestHandler : IRequestHandler<Request, Response>
    {
        private readonly IChatClient _chatClient;
        private readonly ChatOptions _chatOptions;
        private readonly string _prompt;
        private readonly TimeZoneInfo _timeZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");

        public RequestHandler(
            IChatClient chatClient,
            NeighborhoodPlugin neighborhoodPlugin,
            BuildingPlugin buildingPlugin,
            SchoolPlugin schoolPlugin,
            CompanyPlugin companyPlugin,
            BuildingManagementPlugin buildingManagementPlugin,
            AddressPlugin addressPlugin)
        {
            _chatClient = chatClient;
            _chatOptions = new ChatOptions
            {
                ResponseFormat = ChatResponseFormat.Json,
                Temperature = 0.1f,
                Tools = [
                    AIFunctionFactory.Create(neighborhoodPlugin.GetNeighborhoods),
                    AIFunctionFactory.Create(buildingPlugin.GetBuildings),
                    AIFunctionFactory.Create(schoolPlugin.GetSchools),
                    AIFunctionFactory.Create(companyPlugin.GetExclusiveCompanies),
                    AIFunctionFactory.Create(buildingManagementPlugin.GetBuildingManagement),
                    AIFunctionFactory.Create(addressPlugin.GetAddresses)
                ]
            };

            _prompt = File.ReadAllText("Features/Listings/QueryToSearchOptions/prompt.md");

            var jsonSchema = JsonSchema.FromType<SearchCriteriaNLPModel>().ToJson();
            _prompt = _prompt.Replace("{JSON_SCHEMA}", jsonSchema);
        }

        public async Task<Response> Handle(Request request, CancellationToken cancellationToken)
        {
            var currentDate = TimeZoneInfo.ConvertTime(DateTime.Now, _timeZone)
                .ToString("dddd, MMMM dd, yyyy");

            var prompt = $"""
                The current date is {currentDate} EST.
                Week starts on Sunday (US convention).
                ---
                {_prompt}
                {request.SearchText}
                """;
            var result = await _chatClient.GetResponseAsync<SearchCriteriaNLPModel>(
                prompt, _chatOptions, cancellationToken: cancellationToken);
            var viewOptions = result.Result.Adapt<SearchCriteriaViewModel>();

            return new Response(viewOptions, result.Result?.UnprocessedCriteria);
        }
    }
}
