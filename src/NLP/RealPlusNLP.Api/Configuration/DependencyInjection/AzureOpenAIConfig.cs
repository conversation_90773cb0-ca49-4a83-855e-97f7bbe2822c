using System.Reflection;
using System.Text.Encodings.Web;
using System.Text.Json.Serialization;
using System.Text.Unicode;
using Azure.AI.OpenAI;
using Azure.Identity;
using Microsoft.Extensions.AI;
using RealPlusNLP.Api.Configuration.Options;
using RealPlusNLP.Api.Features.Listings.QueryToSearchOptions.Plugins;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class AzureOpenAIConfig
{
    public static IServiceCollection AddAzureOpenAI(
        this IServiceCollection services,
        ConfigurationManager configuration)
    {
        services.AddDistributedMemoryCache(options =>
        {
            options.ExpirationScanFrequency = TimeSpan.FromMinutes(30);
        });

        var options = new AzureOpenAIOptions();
        configuration.GetSection(AzureOpenAIOptions.SectionName).Bind(options);

        services.AddTransient<NeighborhoodPlugin>();
        services.AddTransient<BuildingPlugin>();
        services.AddTransient<SchoolPlugin>();
        services.AddTransient<CompanyPlugin>();
        services.AddTransient<BuildingManagementPlugin>();
        services.AddTransient<AddressPlugin>();

        var assemblyName = Assembly.GetExecutingAssembly().GetName().Name!;

        services
            .AddChatClient(new AzureOpenAIClient(
                    new Uri(configuration[AzureOpenAIOptions.EndpointVariableName]!),
                    new DefaultAzureCredential())
                .GetChatClient(options.ModelId)
                .AsIChatClient())
            .UseDistributedCache()
            .UseOpenTelemetry(
                sourceName: $"{assemblyName}.AzureOpenAI",
                configure: options =>
                {
                    // Enable logging of potentially sensitive data (message content, function calls)
                    options.EnableSensitiveData = true;
                }
            )
            .UseLogging(configure: options =>
            {
                options.JsonSerializerOptions = new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                    Encoder = JavaScriptEncoder.Create(UnicodeRanges.All)
                };
            })
            .UseFunctionInvocation();

        return services;
    }
}
