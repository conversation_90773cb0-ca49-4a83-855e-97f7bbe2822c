﻿using System.Text.Json.Serialization;
using System.Text.Json;
using RealPlusNLP.Api.Configuration.Options;
using RealPlusNLP.Api.Abstractions.Services;
using RealPlusNLP.Api.Infrastructure.Services;
using RealPlusNLP.Api.Common.Middlewares;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class DependencyInjection
{
    public static IServiceCollection AddDependencies(
        this IServiceCollection services,
        ConfigurationManager configuration,
        ILoggingBuilder loggingBuilder,
        IWebHostEnvironment environment)
    {
        services.AddOptions<AuthenticationOptions>()
            .Bind(configuration.GetSection(AuthenticationOptions.SectionName))
            .ValidateDataAnnotations()
            .ValidateOnStart();

        services.AddSingleton<INeighborhoodService, NeighborhoodService>();

        services
            .AddAzureOpenAI(configuration)
            .AddAppApiVersioning()
            .AddProblemHandling()
            .AddMediatr()
            .AddMapsterConfig()
            .AddHealthCheck()
            .AddOpenApiConfig()
            .AddOpenTelemetryConfig(loggingBuilder, environment, configuration)
            .AddElasticsearchConfig(configuration)
            .AddAuthentication()
            .AddDatabase();

        services.AddCarter();

        services.AddEndpointsApiExplorer();
        services.AddHttpContextAccessor();

        services.AddCors(options =>
        {
            options.AddDefaultPolicy(policy =>
            {
                policy.WithOrigins(configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() ?? [])
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });
        services.AddResponseCompression(options =>
        {
            options.EnableForHttps = true;
        });
        services.ConfigureHttpJsonOptions(options =>
        {
            options.SerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
            options.SerializerOptions.PropertyNameCaseInsensitive = true;
            options.SerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
        });

        return services;
    }

    public static void MapAndUseDependencies(this WebApplication app)
    {
        app.UseCors();
        app.UseHttpsRedirection();
        app.UseResponseCompression();
        app.UseProblemHandling();

        app.MapHealthCheck();
        app.MapAppApiVersioning();
        app.MapOpenApiConfig();

        app.UseMiddleware<UserContextEnrichmentMiddleware>();

        app.MapGet("/", () => "RealPlus NLP API is running...")
            .ExcludeFromDescription();
    }
}
