using RealPlusNLP.Api.Abstractions.Repositories;
using RealPlusNLP.Api.Infrastructure.Repositories;

namespace RealPlusNLP.Api.Configuration.DependencyInjection;

public static class DatabaseConfig
{
    public static IServiceCollection AddDatabase(
        this IServiceCollection services)
    {
        services.AddScoped<ReSourceRepository>();
        services.AddScoped<AllAccessNYCRepository>();
        services.AddScoped<IRepositoryFactory, RepositoryFactory>();

        return services;
    }
}
