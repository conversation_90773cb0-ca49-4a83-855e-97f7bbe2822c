using RealPlusNLP.Api.Common.Models;

namespace RealPlusNLP.Api.Abstractions.Repositories;

public interface IDataRepository
{
    Task<IReadOnlyCollection<AddressModel>> GetAddressesAsync(
        int BuildingNumberStart,
        int BuildingNumberEnd,
        string BuildingNumberFull,
        string FullAddress,
        string StreetName,
        CancellationToken cancellationToken = default
    );
}
